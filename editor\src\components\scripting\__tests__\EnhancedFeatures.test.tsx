/**
 * 增强功能测试
 * 测试可视化编辑器的所有增强功能
 */
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { I18nextProvider } from 'react-i18next';
import i18n from '../../../i18n';

// 导入要测试的组件和服务
import ScriptTemplates from '../ScriptTemplates';
import CodeEditor from '../CodeEditor';
import VisualScriptEditor from '../VisualScriptEditor';
import ScriptEditorSettingsPanel from '../../settings/ScriptEditorSettingsPanel';
import KeyboardShortcutService from '../../../services/KeyboardShortcutService';
import ThemeService from '../../../services/ThemeService';
import ScriptCacheService from '../../../services/ScriptCacheService';

// Mock store
const mockStore = configureStore({
  reducer: {
    ui: (state = { theme: 'light' }) => state,
    editor: (state = {}) => state
  }
});

// 测试工具函数
const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <Provider store={mockStore}>
      <I18nextProvider i18n={i18n}>
        {component}
      </I18nextProvider>
    </Provider>
  );
};

describe('脚本模板功能', () => {
  test('应该显示模板列表', () => {
    const mockOnClose = jest.fn();
    const mockOnSelectTemplate = jest.fn();

    renderWithProviders(
      <ScriptTemplates
        visible={true}
        onClose={mockOnClose}
        onSelectTemplate={mockOnSelectTemplate}
      />
    );

    expect(screen.getByText('选择脚本模板')).toBeInTheDocument();
    expect(screen.getByText('基础JavaScript脚本')).toBeInTheDocument();
    expect(screen.getByText('玩家控制器')).toBeInTheDocument();
  });

  test('应该支持模板搜索', async () => {
    const mockOnClose = jest.fn();
    const mockOnSelectTemplate = jest.fn();

    renderWithProviders(
      <ScriptTemplates
        visible={true}
        onClose={mockOnClose}
        onSelectTemplate={mockOnSelectTemplate}
      />
    );

    const searchInput = screen.getByPlaceholderText('搜索模板...');
    fireEvent.change(searchInput, { target: { value: '玩家' } });

    await waitFor(() => {
      expect(screen.getByText('玩家控制器')).toBeInTheDocument();
    });
  });

  test('应该支持模板分类过滤', () => {
    const mockOnClose = jest.fn();
    const mockOnSelectTemplate = jest.fn();

    renderWithProviders(
      <ScriptTemplates
        visible={true}
        onClose={mockOnClose}
        onSelectTemplate={mockOnSelectTemplate}
      />
    );

    const gameplayButton = screen.getByText('游戏玩法');
    fireEvent.click(gameplayButton);

    expect(screen.getByText('玩家控制器')).toBeInTheDocument();
  });

  test('应该支持模板收藏功能', () => {
    const mockOnClose = jest.fn();
    const mockOnSelectTemplate = jest.fn();

    renderWithProviders(
      <ScriptTemplates
        visible={true}
        onClose={mockOnClose}
        onSelectTemplate={mockOnSelectTemplate}
      />
    );

    // 查找收藏按钮并点击
    const favoriteButtons = screen.getAllByRole('button');
    const favoriteButton = favoriteButtons.find(button => 
      button.querySelector('.anticon-star')
    );
    
    if (favoriteButton) {
      fireEvent.click(favoriteButton);
    }
  });
});

describe('代码编辑器增强功能', () => {
  test('应该显示智能提示', async () => {
    const mockOnChange = jest.fn();

    renderWithProviders(
      <CodeEditor
        value=""
        language="javascript" as const
        onChange={mockOnChange}
        enableAutoComplete={true}
      />
    );

    const textarea = screen.getByRole('textbox');
    fireEvent.change(textarea, { target: { value: 'con' } });

    // 模拟智能提示显示
    await waitFor(() => {
      // 这里应该检查智能提示是否显示
      // 由于实际实现可能需要更复杂的DOM结构，这里只是示例
    });
  });

  test('应该支持全屏模式', () => {
    const mockOnChange = jest.fn();

    renderWithProviders(
      <CodeEditor
        value="console.log('test');"
        language="javascript" as const
        onChange={mockOnChange}
      />
    );

    const fullscreenButton = screen.getByRole('button', { name: /全屏/ });
    fireEvent.click(fullscreenButton);

    // 检查全屏状态
    expect(fullscreenButton).toBeInTheDocument();
  });

  test('应该支持代码复制功能', () => {
    const mockOnChange = jest.fn();

    // Mock clipboard API
    Object.assign(navigator, {
      clipboard: {
        writeText: jest.fn().mockImplementation(() => Promise.resolve()),
      },
    });

    renderWithProviders(
      <CodeEditor
        value="console.log('test');"
        language="javascript" as const
        onChange={mockOnChange}
      />
    );

    const copyButton = screen.getByRole('button', { name: /复制/ });
    fireEvent.click(copyButton);

    expect(navigator.clipboard.writeText).toHaveBeenCalledWith("console.log('test');");
  });
});

describe('可视化脚本编辑器', () => {
  test('应该显示节点搜索功能', () => {
    const mockOnChange = jest.fn();

    renderWithProviders(
      <VisualScriptEditor
        value={{ nodes: [], connections: [], variables: [], customEvents: [] }}
        onChange={mockOnChange}
      />
    );

    const addNodeButton = screen.getByText('添加节点');
    fireEvent.click(addNodeButton);

    // 检查节点搜索抽屉是否打开
    expect(screen.getByText('选择节点')).toBeInTheDocument();
  });

  test('应该支持脚本执行控制', () => {
    const mockOnChange = jest.fn();
    const mockOnExecutionStateChange = jest.fn();

    renderWithProviders(
      <VisualScriptEditor
        value={{ nodes: [], connections: [], variables: [], customEvents: [] }}
        onChange={mockOnChange}
        onExecutionStateChange={mockOnExecutionStateChange}
      />
    );

    const playButton = screen.getByRole('button', { name: /运行/ });
    fireEvent.click(playButton);

    expect(mockOnExecutionStateChange).toHaveBeenCalledWith('running');
  });
});

describe('键盘快捷键服务', () => {
  let keyboardService: KeyboardShortcutService;

  beforeEach(() => {
    keyboardService = KeyboardShortcutService.getInstance();
    keyboardService.startListening();
  });

  afterEach(() => {
    keyboardService.stopListening();
    keyboardService.cleanup();
  });

  test('应该注册默认快捷键', () => {
    const shortcuts = keyboardService.getAllShortcuts();
    expect(shortcuts.length).toBeGreaterThan(0);
    
    const saveShortcut = shortcuts.find(s => s.id === 'file.save');
    expect(saveShortcut).toBeDefined();
    expect(saveShortcut?.keys).toEqual(['Ctrl', 'S']);
  });

  test('应该支持快捷键切换', () => {
    keyboardService.toggleShortcut('file.save');
    const shortcuts = keyboardService.getAllShortcuts();
    const saveShortcut = shortcuts.find(s => s.id === 'file.save');
    
    // 切换后状态应该改变
    expect(saveShortcut?.enabled).toBeDefined();
  });

  test('应该支持自定义快捷键', () => {
    keyboardService.updateShortcut('file.save', { keys: ['Ctrl', 'Shift', 'S'] });
    const shortcuts = keyboardService.getAllShortcuts();
    const saveShortcut = shortcuts.find(s => s.id === 'file.save');
    
    expect(saveShortcut?.keys).toEqual(['Ctrl', 'Shift', 'S']);
  });
});

describe('主题服务', () => {
  let themeService: ThemeService;

  beforeEach(() => {
    themeService = ThemeService.getInstance();
  });

  afterEach(() => {
    themeService.cleanup();
  });

  test('应该提供预定义主题', () => {
    const themes = themeService.getAllThemes();
    expect(themes.length).toBeGreaterThan(0);
    
    const lightTheme = themes.find(t => t.id === 'light');
    const darkTheme = themes.find(t => t.id === 'dark');
    
    expect(lightTheme).toBeDefined();
    expect(darkTheme).toBeDefined();
  });

  test('应该支持主题切换', () => {
    themeService.setTheme('dark');
    const currentTheme = themeService.getCurrentTheme();
    
    expect(currentTheme?.id).toBe('dark');
    expect(currentTheme?.type).toBe('dark');
  });

  test('应该支持自定义主题', () => {
    const customThemeId = themeService.createCustomTheme({
      name: '测试主题',
      description: '测试用的自定义主题',
      type: 'light',
      colors: {
        primary: '#ff0000',
        secondary: '#00ff00',
        background: '#ffffff',
        surface: '#f5f5f5',
        text: '#000000',
        textSecondary: '#666666',
        border: '#cccccc',
        success: '#00ff00',
        warning: '#ffff00',
        error: '#ff0000',
        info: '#0000ff'
      }
    });

    expect(customThemeId).toBeDefined();
    
    const themes = themeService.getAllThemes();
    const customTheme = themes.find(t => t.id === customThemeId);
    
    expect(customTheme).toBeDefined();
    expect(customTheme?.name).toBe('测试主题');
  });
});

describe('脚本缓存服务', () => {
  let cacheService: ScriptCacheService;

  beforeEach(() => {
    cacheService = ScriptCacheService.getInstance();
    cacheService.clear(); // 清空缓存开始测试
  });

  afterEach(() => {
    cacheService.cleanup();
  });

  test('应该支持缓存设置和获取', () => {
    const scriptId = 'test-script';
    const content = 'console.log("test");';
    const compiledResult = { type: 'function', code: 'compiled code' };

    // 设置缓存
    cacheService.set(scriptId, content, compiledResult);

    // 获取缓存
    const cached = cacheService.get(scriptId, content);
    expect(cached).toEqual(compiledResult);
  });

  test('应该在内容变化时缓存失效', () => {
    const scriptId = 'test-script';
    const content1 = 'console.log("test1");';
    const content2 = 'console.log("test2");';
    const compiledResult = { type: 'function', code: 'compiled code' };

    // 设置缓存
    cacheService.set(scriptId, content1, compiledResult);

    // 用不同内容获取缓存应该返回null
    const cached = cacheService.get(scriptId, content2);
    expect(cached).toBeNull();
  });

  test('应该提供缓存统计信息', () => {
    const scriptId = 'test-script';
    const content = 'console.log("test");';
    const compiledResult = { type: 'function', code: 'compiled code' };

    cacheService.set(scriptId, content, compiledResult);
    cacheService.get(scriptId, content); // 命中缓存

    const stats = cacheService.getStats();
    expect(stats.totalEntries).toBe(1);
    expect(stats.totalHits).toBe(1);
    expect(stats.hitRate).toBe(1);
  });

  test('应该支持缓存配置更新', () => {
    const newConfig = {
      maxSize: 100 * 1024 * 1024, // 100MB
      maxEntries: 2000,
      ttl: 48 * 60 * 60 * 1000 // 48小时
    };

    cacheService.updateConfig(newConfig);
    const config = cacheService.getConfig();

    expect(config.maxSize).toBe(newConfig.maxSize);
    expect(config.maxEntries).toBe(newConfig.maxEntries);
    expect(config.ttl).toBe(newConfig.ttl);
  });
});

describe('设置面板', () => {
  test('应该显示所有设置选项', () => {
    renderWithProviders(
      <ScriptEditorSettingsPanel
        visible={true}
        onClose={() => {}}
      />
    );

    expect(screen.getByText('脚本编辑器设置')).toBeInTheDocument();
    expect(screen.getByText('常规')).toBeInTheDocument();
    expect(screen.getByText('快捷键')).toBeInTheDocument();
    expect(screen.getByText('缓存')).toBeInTheDocument();
  });

  test('应该支持设置导出', () => {
    renderWithProviders(
      <ScriptEditorSettingsPanel
        visible={true}
        onClose={() => {}}
      />
    );

    const exportButton = screen.getByText('导出设置');
    expect(exportButton).toBeInTheDocument();
  });
});

describe('集成测试', () => {
  test('所有服务应该能够正常协作', () => {
    const keyboardService = KeyboardShortcutService.getInstance();
    const themeService = ThemeService.getInstance();
    const cacheService = ScriptCacheService.getInstance();

    // 测试服务初始化
    expect(keyboardService).toBeDefined();
    expect(themeService).toBeDefined();
    expect(cacheService).toBeDefined();

    // 测试服务功能
    const shortcuts = keyboardService.getAllShortcuts();
    const themes = themeService.getAllThemes();
    const cacheStats = cacheService.getStats();

    expect(shortcuts.length).toBeGreaterThan(0);
    expect(themes.length).toBeGreaterThan(0);
    expect(cacheStats).toBeDefined();

    // 清理
    keyboardService.cleanup();
    themeService.cleanup();
    cacheService.cleanup();
  });
});
